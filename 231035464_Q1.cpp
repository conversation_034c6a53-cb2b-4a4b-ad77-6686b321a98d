#include <stdio.h>
#include <stdlib.h>

//Matricula 231035464
//<PERSON><PERSON>

typedef struct process {
    int tempo, tempo_contado, id, finalizado;
    long long tempo_final;
} processo;

int main() {
	int quantidade, tempo;
	int id;
	int tempo_exec;
	scanf(" %d", &quantidade);
	scanf(" %d", &tempo);
	processo *processos = (processo*)malloc(sizeof(processo) * quantidade);
	for (int i = 0; i < quantidade; i++) {
		scanf(" %d %d", &id, &tempo_exec);
		processos[i].tempo = tempo_exec * 1000;
		processos[i].id = id;
		processos[i].tempo_contado = 0;
		processos[i].finalizado = 0;
	}
	int *fila = (int*)malloc(sizeof(int) * (quantidade+1));
	int inicio = 0, fim = quantidade;
	for (int i = 0; i < quantidade+1; i++) {
		fila[i] = i;
	}
	processo *finalizados = (processo*)malloc(sizeof(processo) * quantidade);
	int prontos = 0;
	long long tempo_global = 0;
	while (inicio !=fim) {
		int j = fila[inicio++];
		if(inicio==quantidade+1){
			inicio = 0;
		}
		processo *p = &processos[j];
		int tempo_restante = p->tempo - p->tempo_contado;
		if (tempo_restante > tempo) {

			p->tempo_contado += tempo;
			tempo_global += tempo;
			fila[fim++] = j;
			if (fim ==quantidade+1){
				fim =0;
			}
		}
		else {
			p->tempo_contado += tempo_restante;
			tempo_global += tempo_restante;
			p->tempo_final = tempo_global;
			p->finalizado = 1;
			finalizados[prontos++] = *p;
		}
	}
	for (int i = 0; i < prontos; i++) {
		printf("%d (%lld)\n", finalizados[i].id, finalizados[i].tempo_final);
	}
	free(processos);
	free(finalizados);
	free(fila);
	return 0;
}

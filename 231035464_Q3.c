
#include <string.h>
#include <stdio.h>
#include <stdlib.h>
#include <pthread.h>
#include <unistd.h>
#include <errno.h>
#include <sys/time.h>
#include <sys/wait.h>

//Matricula 231035464
//No<PERSON> Lucas <PERSON> Arruda
int main(){

        double tempo = 0.0;
        char arg1[256];
        char arg2[256];
        int estado;
        while (scanf("%255s %255s", arg1, arg2)==2){
                fflush(stdout);
                struct timeval inicio, fim;
                gettimeofday(&inicio, NULL);
                pid_t filho = fork();
                if(filho == 0){
                        execl(arg1, arg1, arg2, NULL);
			printf("> Erro: %s\n", strerror(errno));
			fclose(stdin);
                        exit(2);
}
                if(filho>0){

                        wait(&estado);
                        gettimeofday(&fim, NULL);
                        double tempo_processo = (fim.tv_sec - inicio.tv_sec)+(fim.tv_usec - inicio.tv_usec)/1000000.0;
                        int retorno = WEXITSTATUS(estado);
                        tempo += tempo_processo;
                        
                        printf("> Demorou %.1f segundos, retornou %d\n", tempo_processo, WEXITSTATUS(estado));
			fflush(stdout);
                }
		else{
			exit(1);
}
        }
        printf(">> O tempo total foi de %.1f segundos\n", tempo);
        return 0;
}

#include <stdio.h>
#include <stdlib.h>
#include <signal.h>
#include <unistd.h>
#include <sys/wait.h>
int count = 0;
pid_t pid;
void handler(int sinal){
	count++;
}

int main() {

	signal(SIGUSR1, handler);
	signal(SIGUSR2, handler);
	while(1) {
		pause();
		if(count == 1){
			pid = fork();
			if(pid == 0){
				exit(0);
			}
		}
		else if(count == 2){
			wait(NULL);
		}
		else if(count == 3){
			exit(0);
		}
    	}

	return 0;
}


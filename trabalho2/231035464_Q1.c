#include <stdio.h>
#include <stdlib.h>

typedef struct Resultado{
    int tempo;
    int id;
}Resultado;

int main(){
    int n;
    int tempo = 0, concluido = 0;
    scanf(" %d", &n);
    int inicio = 0, fim = n;
    int fila[9879];
    int ponteiro[n];
    Resultado resul[n];

    int ins_processo[n];
    for (int i = 0; i<n; i++){
        scanf(" %d", &ins_processo[i]);
    }
    
    int processos[n][105];
    for (int i = 0; i<n; i++){
        for (int j=0; j<ins_processo[i]; j++){
            scanf(" %d", &processos[i][j]);
        }
    }

    for (int i = 0; i < n; i++) {
        fila[i] = i;
        ponteiro[i] = 0;
    }
    while (inicio < fim) {
        int atual = fila[inicio++];
        int pos = ponteiro[atual];

        if (pos >= ins_processo[atual]) {
            continue;
        }

        if (processos[atual][pos] == 0) {
            tempo += 10;
            ponteiro[atual]++;
            if (ponteiro[atual] == ins_processo[atual]) {
                resul[concluido].id = atual + 1;
                resul[concluido].tempo = tempo;
                concluido++;
            } else {
                fila[fim++] = atual; // volta para o fim da fila
            }
        } else {
            processos[atual][pos] = 0; // vira não blocante
            fila[fim++] = atual;
            // não avança ponteiro aqui!
        }
    }
    for (int i = 0; i < concluido; i++) {
        printf("%d (%d)\n", resul[i].id, resul[i].tempo);
    }
    return 0;
}
